<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ - GreenMailer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10B981',
                        'primary-dark': '#059669',
                        secondary: '#3B82F6',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <span class="text-2xl font-bold text-gray-800">GreenMailer</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-primary transition">Home</a>
                    <a href="features.html" class="text-gray-600 hover:text-primary transition">Features</a>
                    <a href="pricing.html" class="text-gray-600 hover:text-primary transition">Pricing</a>
                    <a href="about.html" class="text-gray-600 hover:text-primary transition">About</a>
                    <a href="contact.html" class="text-gray-600 hover:text-primary transition">Contact</a>
                    <a href="faq.html" class="text-primary font-semibold">FAQ</a>
                    <a href="terms.html" class="text-gray-600 hover:text-primary transition">Terms</a>
                </div>
                
                <!-- CTA Button -->
                <div class="hidden md:block">
                    <a href="#" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg transition">
                        Get Started
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <button class="md:hidden" id="mobile-menu-btn">
                    <i class="fas fa-bars text-gray-600"></i>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600">Home</a>
                    <a href="features.html" class="text-gray-600">Features</a>
                    <a href="pricing.html" class="text-gray-600">Pricing</a>
                    <a href="about.html" class="text-gray-600">About</a>
                    <a href="contact.html" class="text-gray-600">Contact</a>
                    <a href="faq.html" class="text-primary font-semibold">FAQ</a>
                    <a href="terms.html" class="text-gray-600">Terms</a>
                    <a href="#" class="bg-primary text-white px-4 py-2 rounded-lg text-center">Get Started</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary to-primary-dark text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
                Frequently Asked Questions
            </h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">
                Find quick answers to common questions about GreenMailer
            </p>
        </div>
    </section>

    <!-- Search Bar -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4 max-w-2xl">
            <div class="relative">
                <input type="text" id="faq-search" placeholder="Search for answers..." 
                    class="w-full px-6 py-4 pl-12 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>
    </section>

    <!-- FAQ Categories -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="faq-category-btn active px-6 py-3 bg-primary text-white rounded-lg font-semibold transition" data-category="all">
                    All Questions
                </button>
                <button class="faq-category-btn px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition" data-category="getting-started">
                    Getting Started
                </button>
                <button class="faq-category-btn px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition" data-category="features">
                    Features
                </button>
                <button class="faq-category-btn px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition" data-category="billing">
                    Billing
                </button>
                <button class="faq-category-btn px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition" data-category="technical">
                    Technical
                </button>
                <button class="faq-category-btn px-6 py-3 bg-gray-200 text-gray-600 rounded-lg font-semibold hover:bg-gray-300 transition" data-category="integrations">
                    Integrations
                </button>
            </div>

            <!-- FAQ Items -->
            <div class="max-w-4xl mx-auto space-y-4" id="faq-container">
                <!-- Getting Started -->
                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="getting-started">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">How do I get started with GreenMailer?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Getting started with GreenMailer is easy! Simply install our app from the Shopify App Store, 
                            connect your store, and you'll be guided through a quick setup process. Within minutes, you'll 
                            be ready to create your first email campaign using our drag-and-drop editor.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="getting-started">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Do I need any technical knowledge to use GreenMailer?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Not at all! GreenMailer is designed for merchants of all technical levels. Our intuitive 
                            drag-and-drop editor, pre-built templates, and automated workflows mean you can create 
                            professional email campaigns without any coding knowledge.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="getting-started">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Is there a free trial available?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! We offer a 14-day free trial with access to all features. No credit card required to start. 
                            You can send up to 1,000 emails during your trial period to test out all of GreenMailer's capabilities.
                        </p>
                    </div>
                </div>

                <!-- Features -->
                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="features">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">What email automation features are available?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            GreenMailer offers comprehensive automation including welcome series, abandoned cart recovery, 
                            post-purchase follow-ups, re-engagement campaigns, and birthday/anniversary emails. You can 
                            create custom automation workflows based on customer behavior and purchase history.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="features">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Can I segment my email lists?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Absolutely! You can create segments based on purchase behavior, geographic location, 
                            engagement levels, product preferences, and custom attributes. This allows you to send 
                            highly targeted campaigns that resonate with specific customer groups.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="features">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">How many email templates are included?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            We provide 50+ professionally designed, mobile-responsive templates covering various 
                            industries and use cases. All templates are fully customizable and new templates are 
                            added regularly based on customer feedback and design trends.
                        </p>
                    </div>
                </div>

                <!-- Billing -->
                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="billing">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">How is pricing calculated?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Our pricing is based on the number of subscribers in your account and emails sent per month. 
                            We offer three main plans: Starter ($19/month), Professional ($49/month), and Enterprise ($99/month). 
                            All plans include unlimited automation and access to all features.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="billing">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Can I change my plan at any time?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Yes, you can upgrade or downgrade your plan at any time from your account settings. 
                            When upgrading, you'll be charged the prorated difference immediately. When downgrading, 
                            the change takes effect at your next billing cycle.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="billing">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">What happens if I exceed my plan limits?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            If you exceed your subscriber or email limits, we'll notify you and provide options to upgrade 
                            your plan or purchase additional credits. Your campaigns won't be interrupted, and we'll work 
                            with you to find the best solution for your needs.
                        </p>
                    </div>
                </div>

                <!-- Technical -->
                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="technical">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">What email deliverability rates can I expect?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            GreenMailer maintains industry-leading deliverability rates of 99%+. We use advanced authentication 
                            protocols, maintain strong sender reputation, and work with major ISPs to ensure your emails 
                            reach the inbox, not the spam folder.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="technical">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Is my data secure with GreenMailer?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Absolutely. We use enterprise-grade security including SSL encryption, secure data centers, 
                            regular security audits, and GDPR compliance. Your customer data and email content are 
                            protected with the highest security standards.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="technical">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">What kind of analytics and reporting do you provide?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Our analytics dashboard provides comprehensive insights including open rates, click-through rates, 
                            conversion tracking, revenue attribution, A/B test results, and customer journey mapping. 
                            You can also export reports and integrate with Google Analytics.
                        </p>
                    </div>
                </div>

                <!-- Integrations -->
                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="integrations">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">How does the Shopify integration work?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Our Shopify integration is seamless and real-time. Once installed, GreenMailer automatically 
                            syncs your customer data, order history, and product information. This enables personalized 
                            campaigns, product recommendations, and behavioral triggers based on actual purchase data.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="integrations">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">Can I integrate with other tools and platforms?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! GreenMailer integrates with popular tools including Google Analytics, Facebook Pixel, 
                            Klaviyo (for migrations), Zapier, and many others. We also provide a REST API for custom 
                            integrations and regularly add new integrations based on customer requests.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg shadow-lg overflow-hidden" data-category="integrations">
                    <button class="faq-toggle w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <h3 class="text-lg font-semibold text-gray-800">How do I migrate from my current email platform?</h3>
                        <i class="fas fa-chevron-down text-primary transition-transform duration-200"></i>
                    </button>
                    <div class="faq-content hidden px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            We make migration easy with our dedicated migration tools and support team. We can import 
                            your subscriber lists, templates, and automation workflows from most major platforms including 
                            Mailchimp, Klaviyo, Constant Contact, and others. Our team provides free migration assistance.
                        </p>
                    </div>
                </div>
            </div>

            <!-- No Results Message -->
            <div id="no-results" class="hidden text-center py-12">
                <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-semibold text-gray-800 mb-2">No results found</h3>
                <p class="text-gray-600 mb-6">Try adjusting your search terms or browse all questions.</p>
                <button class="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-dark transition" onclick="clearSearch()">
                    Show All Questions
                </button>
            </div>
        </div>
    </section>

    <!-- Still Need Help -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Still need help?</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Can't find the answer you're looking for? Our support team is here to help.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="contact.html" class="bg-primary hover:bg-primary-dark text-white px-8 py-4 rounded-lg font-semibold transition">
                    Contact Support
                </a>
                <a href="#" class="border-2 border-primary text-primary px-8 py-4 rounded-lg font-semibold hover:bg-primary hover:text-white transition">
                    Live Chat
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <span class="text-2xl font-bold">GreenMailer</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        The ultimate email marketing solution for Shopify stores.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Product</h4>
                    <ul class="space-y-3">
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition">Features</a></li>
                        <li><a href="pricing.html" class="text-gray-400 hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Company</h4>
                    <ul class="space-y-3">
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition">About</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Careers</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition">FAQ</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Help Center</a></li>
                        <li><a href="terms.html" class="text-gray-400 hover:text-white transition">Terms</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Privacy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 GreenMailer. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // FAQ Toggle functionality
        document.querySelectorAll('.faq-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = this.querySelector('i');
                
                // Toggle content
                content.classList.toggle('hidden');
                
                // Toggle icon
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-up');
                
                // Add slight rotation animation
                icon.style.transform = content.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(180deg)';
            });
        });

        // Category filtering
        document.querySelectorAll('.faq-category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const category = this.dataset.category;
                
                // Update active button
                document.querySelectorAll('.faq-category-btn').forEach(b => {
                    b.classList.remove('active', 'bg-primary', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-600');
                });
                this.classList.add('active', 'bg-primary', 'text-white');
                this.classList.remove('bg-gray-200', 'text-gray-600');
                
                // Filter FAQ items
                filterFAQs(category);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('faq-search');
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            if (searchTerm) {
                searchFAQs(searchTerm);
            } else {
                showAllFAQs();
            }
        });

        function filterFAQs(category) {
            const faqItems = document.querySelectorAll('.faq-item');
            let visibleCount = 0;
            
            faqItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });
            
            document.getElementById('no-results').classList.add('hidden');
        }

        function searchFAQs(searchTerm) {
            const faqItems = document.querySelectorAll('.faq-item');
            let visibleCount = 0;
            
            faqItems.forEach(item => {
                const question = item.querySelector('h3').textContent.toLowerCase();
                const answer = item.querySelector('.faq-content p').textContent.toLowerCase();
                
                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });
            
            if (visibleCount === 0) {
                document.getElementById('no-results').classList.remove('hidden');
            } else {
                document.getElementById('no-results').classList.add('hidden');
            }
        }

        function showAllFAQs() {
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                item.style.display = 'block';
            });
            document.getElementById('no-results').classList.add('hidden');
            
            // Reset category filter to "All"
            document.querySelectorAll('.faq-category-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-primary', 'text-white');
                btn.classList.add('bg-gray-200', 'text-gray-600');
            });
            document.querySelector('.faq-category-btn[data-category="all"]').classList.add('active', 'bg-primary', 'text-white');
            document.querySelector('.faq-category-btn[data-category="all"]').classList.remove('bg-gray-200', 'text-gray-600');
        }

        function clearSearch() {
            document.getElementById('faq-search').value = '';
            showAllFAQs();
        }
    </script>
</body>
</html>