<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Details - GreenMailer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10B981',
                        'primary-dark': '#059669',
                        secondary: '#3B82F6',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <span class="text-2xl font-bold text-gray-800">GreenMailer</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-primary transition">Home</a>
                    <a href="features.html" class="text-primary font-semibold">Features</a>
                    <a href="pricing.html" class="text-gray-600 hover:text-primary transition">Pricing</a>
                    <a href="about.html" class="text-gray-600 hover:text-primary transition">About</a>
                    <a href="contact.html" class="text-gray-600 hover:text-primary transition">Contact</a>
                    <a href="faq.html" class="text-gray-600 hover:text-primary transition">FAQ</a>
                    <a href="terms.html" class="text-gray-600 hover:text-primary transition">Terms</a>
                </div>
                
                <!-- CTA Button -->
                <div class="hidden md:block">
                    <a href="#" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg transition">
                        Get Started
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <button class="md:hidden" id="mobile-menu-btn">
                    <i class="fas fa-bars text-gray-600"></i>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600">Home</a>
                    <a href="features.html" class="text-primary font-semibold">Features</a>
                    <a href="pricing.html" class="text-gray-600">Pricing</a>
                    <a href="about.html" class="text-gray-600">About</a>
                    <a href="contact.html" class="text-gray-600">Contact</a>
                    <a href="faq.html" class="text-gray-600">FAQ</a>
                    <a href="terms.html" class="text-gray-600">Terms</a>
                    <a href="#" class="bg-primary text-white px-4 py-2 rounded-lg text-center">Get Started</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Breadcrumb -->
    <section class="bg-white py-4 border-b">
        <div class="container mx-auto px-4">
            <nav class="flex text-sm">
                <a href="index.html" class="text-gray-500 hover:text-primary">Home</a>
                <span class="mx-2 text-gray-400">/</span>
                <a href="features.html" class="text-gray-500 hover:text-primary">Features</a>
                <span class="mx-2 text-gray-400">/</span>
                <span class="text-gray-800" id="breadcrumb-current">Feature Details</span>
            </nav>
        </div>
    </section>

    <!-- Feature Details Content -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <!-- Feature Header -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="flex flex-col lg:flex-row items-start lg:items-center gap-8">
                    <div class="flex-1">
                        <div class="flex items-center mb-4">
                            <div id="feature-icon" class="w-20 h-20 bg-primary rounded-lg flex items-center justify-center mr-6">
                                <i class="fas fa-paint-brush text-white text-3xl"></i>
                            </div>
                            <div>
                                <h1 id="feature-title" class="text-4xl font-bold text-gray-800 mb-2">Drag & Drop Email Editor</h1>
                                <p id="feature-subtitle" class="text-xl text-gray-600">Create stunning emails without any coding knowledge</p>
                            </div>
                        </div>
                        <p id="feature-description" class="text-gray-600 text-lg leading-relaxed">
                            Our intuitive drag-and-drop email editor makes it easy for anyone to create professional, eye-catching email campaigns. 
                            With over 50 pre-designed templates and powerful customization options, you can design emails that perfectly match 
                            your brand and drive conversions.
                        </p>
                    </div>
                    <div class="lg:w-1/3">
                        <div class="bg-gray-100 rounded-lg p-8 text-center">
                            <i class="fas fa-image text-gray-400 text-6xl mb-4"></i>
                            <p class="text-gray-500">Feature Screenshot</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feature Benefits -->
            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Key Benefits</h2>
                    <ul id="feature-benefits" class="space-y-4">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">No Coding Required</h3>
                                <p class="text-gray-600">Create professional emails with simple drag-and-drop actions</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Professional Templates</h3>
                                <p class="text-gray-600">Choose from 50+ beautifully designed, industry-specific templates</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Real-Time Preview</h3>
                                <p class="text-gray-600">See exactly how your email will look on desktop and mobile</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-primary mr-3 mt-1"></i>
                            <div>
                                <h3 class="font-semibold text-gray-800">Brand Customization</h3>
                                <p class="text-gray-600">Match your brand colors, fonts, and logo automatically</p>
                            </div>
                        </li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">How It Works</h2>
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-4 mt-1">
                                <span class="text-white font-bold text-sm">1</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 mb-2">Choose a Template</h3>
                                <p class="text-gray-600">Select from our library of professionally designed templates or start from scratch</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-4 mt-1">
                                <span class="text-white font-bold text-sm">2</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 mb-2">Customize Your Content</h3>
                                <p class="text-gray-600">Drag and drop elements, add your text, images, and branding</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-4 mt-1">
                                <span class="text-white font-bold text-sm">3</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 mb-2">Preview & Test</h3>
                                <p class="text-gray-600">Preview your email across devices and send test emails</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-4 mt-1">
                                <span class="text-white font-bold text-sm">4</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 mb-2">Send or Schedule</h3>
                                <p class="text-gray-600">Send immediately or schedule for the perfect timing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feature Specifications -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Technical Specifications</h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Template Library</h3>
                        <p class="text-gray-600">50+ responsive templates</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Elements</h3>
                        <p class="text-gray-600">20+ drag-and-drop elements</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Image Support</h3>
                        <p class="text-gray-600">JPG, PNG, GIF formats</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Email Clients</h3>
                        <p class="text-gray-600">99% compatibility rate</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Mobile Responsive</h3>
                        <p class="text-gray-600">Automatic optimization</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-gray-800 mb-2">Load Time</h3>
                        <p class="text-gray-600">Under 2 seconds</p>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="bg-gradient-to-r from-primary to-primary-dark rounded-lg p-8 text-center text-white">
                <h2 class="text-3xl font-bold mb-4">Ready to Try This Feature?</h2>
                <p class="text-xl mb-8 opacity-90">Start creating beautiful email campaigns today with our drag-and-drop editor</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#" class="bg-white text-primary px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition">
                        Start Free Trial
                    </a>
                    <a href="features.html" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition">
                        View All Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <span class="text-2xl font-bold">GreenMailer</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        The ultimate email marketing solution for Shopify stores.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Product</h4>
                    <ul class="space-y-3">
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition">Features</a></li>
                        <li><a href="pricing.html" class="text-gray-400 hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Company</h4>
                    <ul class="space-y-3">
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition">About</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Careers</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition">FAQ</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Help Center</a></li>
                        <li><a href="terms.html" class="text-gray-400 hover:text-white transition">Terms</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Privacy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 GreenMailer. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Feature data
        const featureData = {
            editor: {
                title: "Drag & Drop Email Editor",
                subtitle: "Create stunning emails without any coding knowledge",
                description: "Our intuitive drag-and-drop email editor makes it easy for anyone to create professional, eye-catching email campaigns. With over 50 pre-designed templates and powerful customization options, you can design emails that perfectly match your brand and drive conversions.",
                icon: "fas fa-paint-brush",
                iconBg: "bg-primary",
                benefits: [
                    { title: "No Coding Required", desc: "Create professional emails with simple drag-and-drop actions" },
                    { title: "Professional Templates", desc: "Choose from 50+ beautifully designed, industry-specific templates" },
                    { title: "Real-Time Preview", desc: "See exactly how your email will look on desktop and mobile" },
                    { title: "Brand Customization", desc: "Match your brand colors, fonts, and logo automatically" }
                ]
            },
            automation: {
                title: "Smart Email Automation",
                subtitle: "Set it and forget it email sequences that convert",
                description: "Create sophisticated email automation workflows that engage your customers at exactly the right moment. From welcome sequences to abandoned cart recovery, our automation tools help you nurture leads and drive sales 24/7.",
                icon: "fas fa-robot",
                iconBg: "bg-secondary",
                benefits: [
                    { title: "Welcome Sequences", desc: "Automatically greet new subscribers with personalized email series" },
                    { title: "Abandoned Cart Recovery", desc: "Win back lost sales with timely cart abandonment emails" },
                    { title: "Behavioral Triggers", desc: "Send emails based on customer actions and engagement" },
                    { title: "Advanced Workflows", desc: "Create complex automation rules with our visual builder" }
                ]
            },
            analytics: {
                title: "Advanced Analytics & Reporting",
                subtitle: "Track performance and optimize your campaigns",
                description: "Get deep insights into your email campaign performance with our comprehensive analytics dashboard. Track opens, clicks, conversions, and revenue to make data-driven decisions that improve your email marketing ROI.",
                icon: "fas fa-chart-line",
                iconBg: "bg-accent",
                benefits: [
                    { title: "Real-Time Reporting", desc: "Monitor campaign performance as it happens" },
                    { title: "Revenue Attribution", desc: "Track exactly how much revenue each campaign generates" },
                    { title: "A/B Testing", desc: "Test subject lines, content, and send times to optimize performance" },
                    { title: "Customer Insights", desc: "Understand subscriber behavior and engagement patterns" }
                ]
            }
        };

        // Get feature from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const featureParam = urlParams.get('feature') || 'editor';
        
        // Load feature data
        if (featureData[featureParam]) {
            const feature = featureData[featureParam];
            
            document.getElementById('feature-title').textContent = feature.title;
            document.getElementById('feature-subtitle').textContent = feature.subtitle;
            document.getElementById('feature-description').textContent = feature.description;
            document.getElementById('breadcrumb-current').textContent = feature.title;
            
            // Update icon
            const iconElement = document.querySelector('#feature-icon i');
            const iconContainer = document.getElementById('feature-icon');
            iconElement.className = `${feature.icon} text-white text-3xl`;
            iconContainer.className = `w-20 h-20 ${feature.iconBg} rounded-lg flex items-center justify-center mr-6`;
            
            // Update benefits
            const benefitsContainer = document.getElementById('feature-benefits');
            benefitsContainer.innerHTML = '';
            feature.benefits.forEach(benefit => {
                const li = document.createElement('li');
                li.className = 'flex items-start';
                li.innerHTML = `
                    <i class="fas fa-check-circle text-primary mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-800">${benefit.title}</h3>
                        <p class="text-gray-600">${benefit.desc}</p>
                    </div>
                `;
                benefitsContainer.appendChild(li);
            });
            
            // Update page title
            document.title = `${feature.title} - GreenMailer`;
        }
    </script>
</body>
</html>