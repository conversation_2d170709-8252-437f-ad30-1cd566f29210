<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - GreenMailer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10B981',
                        'primary-dark': '#059669',
                        secondary: '#3B82F6',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <span class="text-2xl font-bold text-gray-800">GreenMailer</span>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-primary transition">Home</a>
                    <a href="features.html" class="text-gray-600 hover:text-primary transition">Features</a>
                    <a href="pricing.html" class="text-gray-600 hover:text-primary transition">Pricing</a>
                    <a href="about.html" class="text-gray-600 hover:text-primary transition">About</a>
                    <a href="contact.html" class="text-primary font-semibold">Contact</a>
                    <a href="faq.html" class="text-gray-600 hover:text-primary transition">FAQ</a>
                    <a href="terms.html" class="text-gray-600 hover:text-primary transition">Terms</a>
                </div>
                
                <!-- CTA Button -->
                <div class="hidden md:block">
                    <a href="#" class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-lg transition">
                        Get Started
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <button class="md:hidden" id="mobile-menu-btn">
                    <i class="fas fa-bars text-gray-600"></i>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600">Home</a>
                    <a href="features.html" class="text-gray-600">Features</a>
                    <a href="pricing.html" class="text-gray-600">Pricing</a>
                    <a href="about.html" class="text-gray-600">About</a>
                    <a href="contact.html" class="text-primary font-semibold">Contact</a>
                    <a href="faq.html" class="text-gray-600">FAQ</a>
                    <a href="terms.html" class="text-gray-600">Terms</a>
                    <a href="#" class="bg-primary text-white px-4 py-2 rounded-lg text-center">Get Started</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary to-primary-dark text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
                Get in Touch
            </h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">
                Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
        </div>
    </section>

    <!-- Contact Form & Info -->
    <section class="py-20">
        <div class="container mx-auto px-4">
            <div class="grid lg:grid-cols-3 gap-12">
                <!-- Contact Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-6">Send Us a Message</h2>
                        <form id="contact-form" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name</label>
                                    <input type="text" id="firstName" name="firstName" required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                                <div>
                                    <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                                <input type="email" id="email" name="email" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                            </div>
                            
                            <div>
                                <label for="company" class="block text-sm font-semibold text-gray-700 mb-2">Company (Optional)</label>
                                <input type="text" id="company" name="company"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                            </div>
                            
                            <div>
                                <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                                <select id="subject" name="subject" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="support">Technical Support</option>
                                    <option value="billing">Billing Question</option>
                                    <option value="partnership">Partnership Opportunity</option>
                                    <option value="feedback">Product Feedback</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
                                <textarea id="message" name="message" rows="6" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                    placeholder="Tell us how we can help you..."></textarea>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="newsletter" name="newsletter" class="mr-3 rounded">
                                <label for="newsletter" class="text-sm text-gray-600">
                                    I'd like to receive updates about GreenMailer features and tips
                                </label>
                            </div>
                            
                            <button type="submit" 
                                class="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-4 px-6 rounded-lg transition duration-200">
                                Send Message
                            </button>
                        </form>
                        
                        <!-- Success Message -->
                        <div id="success-message" class="hidden mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <p class="text-green-800">Thank you! Your message has been sent. We'll get back to you soon.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="space-y-8">
                    <!-- Contact Details -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">Contact Information</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Email</h4>
                                    <p class="text-gray-600"><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-phone text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Phone</h4>
                                    <p class="text-gray-600">+****************</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-12 h-12 bg-accent rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Office</h4>
                                    <p class="text-gray-600">123 Business St<br>Suite 100<br>San Francisco, CA 94105</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Business Hours</h4>
                                    <p class="text-gray-600">Monday - Friday<br>9:00 AM - 6:00 PM PST</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Support Resources -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">Need Quick Help?</h3>
                        
                        <div class="space-y-4">
                            <a href="faq.html" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                                <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-question text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">FAQ</h4>
                                    <p class="text-gray-600 text-sm">Find answers to common questions</p>
                                </div>
                            </a>
                            
                            <a href="#" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                                <div class="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-book text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Help Center</h4>
                                    <p class="text-gray-600 text-sm">Browse our knowledge base</p>
                                </div>
                            </a>
                            
                            <a href="#" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                                <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-comments text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Live Chat</h4>
                                    <p class="text-gray-600 text-sm">Chat with our support team</p>
                                </div>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">Follow Us</h3>
                        
                        <div class="flex space-x-4">
                            <a href="#" class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center hover:bg-blue-600 transition">
                                <i class="fab fa-twitter text-white"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition">
                                <i class="fab fa-facebook text-white"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-blue-700 rounded-lg flex items-center justify-center hover:bg-blue-800 transition">
                                <i class="fab fa-linkedin text-white"></i>
                            </a>
                            <a href="#" class="w-12 h-12 bg-pink-500 rounded-lg flex items-center justify-center hover:bg-pink-600 transition">
                                <i class="fab fa-instagram text-white"></i>
                            </a>
                        </div>
                        
                        <p class="text-gray-600 text-sm mt-4">
                            Stay updated with the latest news, tips, and product updates from GreenMailer.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-12 bg-gray-100">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-gray-800 text-center mb-8">Find Us</h2>
            <div class="bg-gray-300 rounded-lg h-96 flex items-center justify-center">
                <div class="text-center">
                    <i class="fas fa-map-marked-alt text-gray-500 text-6xl mb-4"></i>
                    <p class="text-gray-600 text-lg">Interactive map would be embedded here</p>
                    <p class="text-gray-500">123 Business St, Suite 100, San Francisco, CA 94105</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-6">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <span class="text-2xl font-bold">GreenMailer</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        The ultimate email marketing solution for Shopify stores.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Product</h4>
                    <ul class="space-y-3">
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition">Features</a></li>
                        <li><a href="pricing.html" class="text-gray-400 hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Company</h4>
                    <ul class="space-y-3">
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition">About</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Careers</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-6">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition">FAQ</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Help Center</a></li>
                        <li><a href="terms.html" class="text-gray-400 hover:text-white transition">Terms</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition">Privacy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 GreenMailer. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Contact form handling
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            const successMessage = document.getElementById('success-message');
            successMessage.classList.remove('hidden');
            
            // Reset form
            this.reset();
            
            // Scroll to success message
            successMessage.scrollIntoView({ behavior: 'smooth' });
            
            // Hide success message after 5 seconds
            setTimeout(function() {
                successMessage.classList.add('hidden');
            }, 5000);
        });

        // Form validation styling
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.required && !this.value.trim()) {
                    this.classList.add('border-red-500');
                } else {
                    this.classList.remove('border-red-500');
                }
            });
        });
    </script>
</body>
</html>